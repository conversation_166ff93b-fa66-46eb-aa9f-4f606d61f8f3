
# ===========================================
# 环境变量配置文件 (.env)
# 用于存储敏感信息和环境特定配置
# ===========================================

# 应用程序环境标识
APP_NAME=抖音推送下载app_env

# 服务器部署配置
HOST=0.0.0.0
RELOAD=true

# 数据库连接（敏感信息）
#DATABASE_URL=sqlite+aiosqlite:///data/db.sqlite3

# 文件存储路径（环境特定）
NAS_BASE_PATH=D:/Docker/douyin_analysis/download

# JWT 安全配置（敏感信息）
SECRET_KEY=6387965d283ed44bc9b4baca450c958fd9c1edb624d4438b939c8065b0d2c475
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_TIME=1
REFRESH_TOKEN_EXPIRE_TIME=15

# Notion API 配置（敏感信息）
NOTION_API_KEY=
NOTION_DATABASE_ID=

# 功能开关（环境特定）
PUSH_TO_DB=true
PUSH_TO_NOTION=true


