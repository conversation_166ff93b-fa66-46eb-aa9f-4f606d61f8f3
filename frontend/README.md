# FastAPI Auth Frontend Demo

这是一个与您的 FastAPI 后端 API 对接的 React 前端应用，演示了完整的双 token 认证系统。

## 🚀 功能特性

### ✅ 已实现的功能
- **用户注册** - 创建新账户
- **用户登录** - 使用用户名密码登录
- **用户登出** - 安全登出并清除所有 token
- **自动 Token 刷新** - Access token 过期时自动使用 refresh token 刷新
- **受保护的路由** - 需要认证才能访问的页面
- **Token 信息展示** - 实时显示当前的 token 状态
- **API 测试** - 测试受保护的 API 端点

### 🔐 安全特性
- **HttpOnly Cookies** - Refresh token 存储在安全的 HttpOnly cookie 中
- **自动认证头** - Axios 拦截器自动添加 Authorization 头
- **错误处理** - 优雅处理认证失败和 token 过期
- **自动重试** - Token 刷新后自动重试失败的请求

## 📋 安装和运行

### 1. 安装依赖
```bash
cd frontend
npm install
```

### 2. 启动开发服务器
```bash
npm start
```

应用将在 http://localhost:3000 启动

### 3. 确保后端运行
确保您的 FastAPI 后端在 http://localhost:8080 运行：
```bash
# 在项目根目录
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8080
```

## 🧪 测试步骤

### 1. 注册新用户
- 点击 "Sign up here" 链接
- 填写用户名和密码（密码至少6位）
- 点击 "Create Account"

### 2. 登录
- 使用注册的账户登录
- 或者点击 "Quick Fill" 使用测试账户：
  - 用户名: `heygo`
  - 密码: `Heytime01!`

### 3. 体验功能
登录后您可以：
- 查看当前的 access token 和 refresh token 信息
- 测试受保护的 API 端点
- 手动刷新 access token
- 验证 refresh token 状态
- 安全登出

## 🔧 技术实现

### API 自动认证
```javascript
// 自动添加 Authorization 头
api.interceptors.request.use(config => {
  if (accessToken) {
    config.headers.Authorization = `Bearer ${accessToken}`;
  }
  return config;
});

// 自动处理 token 刷新
api.interceptors.response.use(
  response => response,
  async error => {
    if (error.response?.status === 401) {
      // 自动刷新 token 并重试请求
      await refreshToken();
      return api(originalRequest);
    }
  }
);
```

### 安全的 Cookie 处理
```javascript
// 所有请求自动包含 cookies
const api = axios.create({
  withCredentials: true, // 重要：发送 HttpOnly cookies
});
```

## 📁 项目结构

```
frontend/
├── public/
│   └── index.html          # HTML 模板
├── src/
│   ├── components/         # React 组件
│   │   ├── Login.js       # 登录组件
│   │   ├── Signup.js      # 注册组件
│   │   ├── Dashboard.js   # 仪表板（登录后页面）
│   │   └── Navbar.js      # 导航栏
│   ├── contexts/          # React Context
│   │   └── AuthContext.js # 认证状态管理
│   ├── services/          # API 服务
│   │   └── api.js         # API 调用和拦截器
│   ├── App.js             # 主应用组件
│   ├── index.js           # 应用入口
│   └── index.css          # 全局样式
├── package.json           # 项目配置
└── README.md             # 说明文档
```

## 🎯 与后端 API 的对接

### 使用的 API 端点
- `POST /v1/auth/login` - 用户登录
- `POST /v1/auth/signup` - 用户注册
- `POST /v1/auth/logout` - 用户登出
- `POST /v1/auth/refresh` - 刷新 access token
- `GET /v1/auth/refresh/validate` - 验证 refresh token
- `GET /v1/auth/refresh/info` - 获取 refresh token 信息
- `GET /v1/auth/test-swagger-auth` - 测试受保护的端点

### Cookie 配置
确保后端设置了正确的 CORS 和 Cookie 配置：
```python
# 允许跨域请求携带 cookies
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

## 🐛 故障排除

### 1. CORS 错误
确保后端允许来自 `http://localhost:3000` 的跨域请求

### 2. Cookie 不工作
检查：
- 后端是否设置了 `secure=False`（开发环境）
- 前端是否设置了 `withCredentials: true`
- 是否在同一域名下（localhost）

### 3. Token 刷新失败
检查：
- Refresh token 是否过期
- Redis 连接是否正常
- 后端 `/refresh` 端点是否正常工作

## 🎉 享受体验！

这个前端应用完整演示了现代 Web 应用中的认证流程，包括：
- 无缝的用户体验
- 自动的 token 管理
- 安全的认证机制
- 优雅的错误处理

您可以看到在真实应用中，用户完全不需要手动处理 token，一切都是自动化的！
