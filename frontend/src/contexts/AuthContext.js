import React, { createContext, useContext, useState, useEffect } from 'react';
import { authAPI, isAuthenticated, setAccessToken } from '../services/api';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isLoggedIn, setIsLoggedIn] = useState(false);

  // 检查认证状态
  const checkAuthStatus = async () => {
    try {
      if (isAuthenticated()) {
        // 尝试验证 refresh token
        const refreshInfo = await authAPI.validateRefreshToken();
        if (refreshInfo.valid) {
          setUser({ username: refreshInfo.username });
          setIsLoggedIn(true);
        } else {
          // Refresh token 无效，清除认证状态
          handleLogout();
        }
      }
    } catch (error) {
      console.log('Auth check failed:', error);
      handleLogout();
    } finally {
      setLoading(false);
    }
  };

  // 登录
  const login = async (username, password) => {
    try {
      setLoading(true);
      const response = await authAPI.login(username, password);
      
      setUser({ username });
      setIsLoggedIn(true);
      
      return { success: true, data: response };
    } catch (error) {
      console.error('Login failed:', error);
      return { 
        success: false, 
        error: error.response?.data?.detail || 'Login failed' 
      };
    } finally {
      setLoading(false);
    }
  };

  // 注册
  const signup = async (username, password) => {
    try {
      setLoading(true);
      const response = await authAPI.signup(username, password);
      return { success: true, data: response };
    } catch (error) {
      console.error('Signup failed:', error);
      return { 
        success: false, 
        error: error.response?.data?.detail || 'Signup failed' 
      };
    } finally {
      setLoading(false);
    }
  };

  // 登出
  const logout = async () => {
    try {
      await authAPI.logout();
    } catch (error) {
      console.log('Logout request failed:', error);
    } finally {
      handleLogout();
    }
  };

  // 处理登出状态
  const handleLogout = () => {
    setUser(null);
    setIsLoggedIn(false);
    setAccessToken(null);
  };

  // 刷新 token
  const refreshToken = async () => {
    try {
      const response = await authAPI.refreshToken();
      return { success: true, data: response };
    } catch (error) {
      console.error('Token refresh failed:', error);
      handleLogout();
      return { success: false, error: 'Token refresh failed' };
    }
  };

  // 监听登出事件
  useEffect(() => {
    const handleAuthLogout = () => {
      handleLogout();
    };

    window.addEventListener('auth:logout', handleAuthLogout);
    return () => {
      window.removeEventListener('auth:logout', handleAuthLogout);
    };
  }, []);

  // 初始化时检查认证状态
  useEffect(() => {
    checkAuthStatus();
  }, []);

  const value = {
    user,
    isLoggedIn,
    loading,
    login,
    signup,
    logout,
    refreshToken,
    checkAuthStatus,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
