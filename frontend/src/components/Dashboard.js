import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { authAPI, getAccessToken } from '../services/api';

const Dashboard = () => {
  const { user, logout } = useAuth();
  const [refreshTokenInfo, setRefreshTokenInfo] = useState(null);
  const [apiTestResult, setApiTestResult] = useState('');
  const [loading, setLoading] = useState(false);

  // 获取 refresh token 信息
  const fetchRefreshTokenInfo = async () => {
    try {
      const info = await authAPI.getRefreshTokenInfo();
      setRefreshTokenInfo(info);
    } catch (error) {
      console.error('Failed to fetch refresh token info:', error);
      setRefreshTokenInfo({ error: 'Failed to fetch refresh token info' });
    }
  };

  // 测试受保护的 API
  const testProtectedAPI = async () => {
    setLoading(true);
    setApiTestResult('Testing...');
    
    try {
      const result = await authAPI.testAuth();
      setApiTestResult(JSON.stringify(result, null, 2));
    } catch (error) {
      setApiTestResult(`Error: ${error.response?.data?.detail || error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // 手动刷新 token
  const handleRefreshToken = async () => {
    setLoading(true);
    try {
      const result = await authAPI.refreshToken();
      setApiTestResult(`Token refreshed successfully:\n${JSON.stringify(result, null, 2)}`);
      // 重新获取 refresh token 信息
      await fetchRefreshTokenInfo();
    } catch (error) {
      setApiTestResult(`Token refresh failed: ${error.response?.data?.detail || error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // 验证 refresh token
  const validateRefreshToken = async () => {
    setLoading(true);
    try {
      const result = await authAPI.validateRefreshToken();
      setApiTestResult(`Refresh token validation:\n${JSON.stringify(result, null, 2)}`);
    } catch (error) {
      setApiTestResult(`Validation failed: ${error.response?.data?.detail || error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // 组件加载时获取信息
  useEffect(() => {
    fetchRefreshTokenInfo();
  }, []);

  const handleLogout = async () => {
    if (window.confirm('Are you sure you want to logout?')) {
      await logout();
    }
  };

  return (
    <div className="container">
      {/* 用户信息 */}
      <div className="user-info">
        <h3>👋 Welcome, {user?.username}!</h3>
        <p>You are successfully logged in to the FastAPI Auth Demo.</p>
      </div>

      {/* Token 信息 */}
      <div className="card">
        <h3>🔑 Token Information</h3>
        
        <div style={{ marginBottom: '20px' }}>
          <h4>Access Token:</h4>
          <div className="token-display">
            {getAccessToken() || 'No access token found'}
          </div>
        </div>

        {refreshTokenInfo && (
          <div style={{ marginBottom: '20px' }}>
            <h4>Refresh Token Info:</h4>
            <div className="token-display">
              {refreshTokenInfo.error ? 
                refreshTokenInfo.error : 
                JSON.stringify(refreshTokenInfo, null, 2)
              }
            </div>
          </div>
        )}

        <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
          <button 
            className="btn btn-secondary" 
            onClick={fetchRefreshTokenInfo}
            disabled={loading}
          >
            Refresh Token Info
          </button>
          <button 
            className="btn btn-secondary" 
            onClick={handleRefreshToken}
            disabled={loading}
          >
            Refresh Access Token
          </button>
          <button 
            className="btn btn-secondary" 
            onClick={validateRefreshToken}
            disabled={loading}
          >
            Validate Refresh Token
          </button>
        </div>
      </div>

      {/* API 测试 */}
      <div className="card">
        <div className="api-test">
          <h3>🧪 API Testing</h3>
          <p>Test protected endpoints to see how automatic token refresh works:</p>
          
          <div style={{ marginBottom: '20px' }}>
            <button 
              className="btn btn-primary" 
              onClick={testProtectedAPI}
              disabled={loading}
            >
              {loading ? 'Testing...' : 'Test Protected API'}
            </button>
          </div>

          {apiTestResult && (
            <div>
              <h4>API Response:</h4>
              <div className="api-result">
                {apiTestResult}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* 功能说明 */}
      <div className="card">
        <h3>✨ Features Demonstrated</h3>
        <ul style={{ lineHeight: '1.6', color: '#555' }}>
          <li><strong>Automatic Token Management:</strong> Access tokens are automatically refreshed when they expire</li>
          <li><strong>Secure Cookie Storage:</strong> Refresh tokens are stored in HttpOnly cookies</li>
          <li><strong>API Interceptors:</strong> Axios automatically handles authentication headers</li>
          <li><strong>Error Handling:</strong> Graceful handling of authentication failures</li>
          <li><strong>Real-time Updates:</strong> Token information updates automatically</li>
        </ul>
      </div>

      {/* 登出按钮 */}
      <div style={{ textAlign: 'center', marginTop: '30px' }}>
        <button 
          className="btn btn-danger" 
          onClick={handleLogout}
          style={{ padding: '15px 30px', fontSize: '16px' }}
        >
          🚪 Logout
        </button>
      </div>
    </div>
  );
};

export default Dashboard;
