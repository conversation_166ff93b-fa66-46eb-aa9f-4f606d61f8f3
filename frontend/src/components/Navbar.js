import React from 'react';
import { useAuth } from '../contexts/AuthContext';

const Navbar = () => {
  const { user, isLoggedIn, logout } = useAuth();

  const handleLogout = async () => {
    if (window.confirm('Are you sure you want to logout?')) {
      await logout();
    }
  };

  return (
    <nav className="navbar">
      <div className="container">
        <h1>🚀 FastAPI Auth Demo</h1>
        
        <div className="nav-links">
          {isLoggedIn ? (
            <>
              <span>Welcome, {user?.username}!</span>
              <button 
                onClick={handleLogout}
                className="btn btn-secondary"
                style={{ padding: '8px 16px' }}
              >
                Logout
              </button>
            </>
          ) : (
            <span>Please login to continue</span>
          )}
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
