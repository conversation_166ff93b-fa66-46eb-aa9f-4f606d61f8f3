import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';

const Login = ({ onSwitchToSignup }) => {
  const [formData, setFormData] = useState({
    username: '',
    password: '',
  });
  const [message, setMessage] = useState('');
  const [messageType, setMessageType] = useState('');

  const { login, loading } = useAuth();

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setMessage('');

    if (!formData.username || !formData.password) {
      setMessage('Please fill in all fields');
      setMessageType('error');
      return;
    }

    const result = await login(formData.username, formData.password);
    
    if (result.success) {
      setMessage('Login successful! Welcome back!');
      setMessageType('success');
    } else {
      setMessage(result.error);
      setMessageType('error');
    }
  };

  // 快速登录按钮（使用您的测试账号）
  const handleQuickLogin = () => {
    setFormData({
      username: 'heygo',
      password: 'Heytime01!',
    });
  };

  return (
    <div className="card">
      <h2 style={{ marginBottom: '20px', textAlign: 'center', color: '#333' }}>
        🔐 Login to Your Account
      </h2>

      {message && (
        <div className={`alert alert-${messageType === 'error' ? 'error' : 'success'}`}>
          {message}
        </div>
      )}

      <form onSubmit={handleSubmit}>
        <div className="form-group">
          <label htmlFor="username">Username:</label>
          <input
            type="text"
            id="username"
            name="username"
            value={formData.username}
            onChange={handleChange}
            placeholder="Enter your username"
            disabled={loading}
          />
        </div>

        <div className="form-group">
          <label htmlFor="password">Password:</label>
          <input
            type="password"
            id="password"
            name="password"
            value={formData.password}
            onChange={handleChange}
            placeholder="Enter your password"
            disabled={loading}
          />
        </div>

        <div style={{ display: 'flex', gap: '10px', marginBottom: '20px' }}>
          <button 
            type="submit" 
            className="btn btn-primary" 
            disabled={loading}
            style={{ flex: 1 }}
          >
            {loading ? 'Logging in...' : 'Login'}
          </button>
          
          <button 
            type="button" 
            className="btn btn-secondary" 
            onClick={handleQuickLogin}
            disabled={loading}
          >
            Quick Fill
          </button>
        </div>
      </form>

      <div style={{ textAlign: 'center', marginTop: '20px' }}>
        <p style={{ color: '#666' }}>
          Don't have an account?{' '}
          <button
            type="button"
            onClick={onSwitchToSignup}
            style={{
              background: 'none',
              border: 'none',
              color: '#007bff',
              textDecoration: 'underline',
              cursor: 'pointer',
            }}
          >
            Sign up here
          </button>
        </p>
      </div>

      <div style={{ marginTop: '30px', padding: '15px', backgroundColor: '#f8f9fa', borderRadius: '4px' }}>
        <h4 style={{ marginBottom: '10px', color: '#495057' }}>💡 Demo Info:</h4>
        <p style={{ margin: '5px 0', fontSize: '14px', color: '#666' }}>
          <strong>Test Account:</strong> heygo / Heytime01!
        </p>
        <p style={{ margin: '5px 0', fontSize: '14px', color: '#666' }}>
          Click "Quick Fill" to auto-fill the test credentials
        </p>
      </div>
    </div>
  );
};

export default Login;
