import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';

const Signup = ({ onSwitchToLogin }) => {
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    confirmPassword: '',
  });
  const [message, setMessage] = useState('');
  const [messageType, setMessageType] = useState('');

  const { signup, loading } = useAuth();

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setMessage('');

    // 验证表单
    if (!formData.username || !formData.password || !formData.confirmPassword) {
      setMessage('Please fill in all fields');
      setMessageType('error');
      return;
    }

    if (formData.password !== formData.confirmPassword) {
      setMessage('Passwords do not match');
      setMessageType('error');
      return;
    }

    if (formData.password.length < 6) {
      setMessage('Password must be at least 6 characters long');
      setMessageType('error');
      return;
    }

    const result = await signup(formData.username, formData.password);
    
    if (result.success) {
      setMessage('Account created successfully! You can now login.');
      setMessageType('success');
      // 清空表单
      setFormData({
        username: '',
        password: '',
        confirmPassword: '',
      });
      // 3秒后自动切换到登录
      setTimeout(() => {
        onSwitchToLogin();
      }, 3000);
    } else {
      setMessage(result.error);
      setMessageType('error');
    }
  };

  return (
    <div className="card">
      <h2 style={{ marginBottom: '20px', textAlign: 'center', color: '#333' }}>
        📝 Create New Account
      </h2>

      {message && (
        <div className={`alert alert-${messageType === 'error' ? 'error' : 'success'}`}>
          {message}
        </div>
      )}

      <form onSubmit={handleSubmit}>
        <div className="form-group">
          <label htmlFor="username">Username:</label>
          <input
            type="text"
            id="username"
            name="username"
            value={formData.username}
            onChange={handleChange}
            placeholder="Choose a username"
            disabled={loading}
          />
        </div>

        <div className="form-group">
          <label htmlFor="password">Password:</label>
          <input
            type="password"
            id="password"
            name="password"
            value={formData.password}
            onChange={handleChange}
            placeholder="Enter your password (min 6 characters)"
            disabled={loading}
          />
        </div>

        <div className="form-group">
          <label htmlFor="confirmPassword">Confirm Password:</label>
          <input
            type="password"
            id="confirmPassword"
            name="confirmPassword"
            value={formData.confirmPassword}
            onChange={handleChange}
            placeholder="Confirm your password"
            disabled={loading}
          />
        </div>

        <button 
          type="submit" 
          className="btn btn-primary" 
          disabled={loading}
          style={{ width: '100%', marginBottom: '20px' }}
        >
          {loading ? 'Creating Account...' : 'Create Account'}
        </button>
      </form>

      <div style={{ textAlign: 'center', marginTop: '20px' }}>
        <p style={{ color: '#666' }}>
          Already have an account?{' '}
          <button
            type="button"
            onClick={onSwitchToLogin}
            style={{
              background: 'none',
              border: 'none',
              color: '#007bff',
              textDecoration: 'underline',
              cursor: 'pointer',
            }}
          >
            Login here
          </button>
        </p>
      </div>

      <div style={{ marginTop: '30px', padding: '15px', backgroundColor: '#f8f9fa', borderRadius: '4px' }}>
        <h4 style={{ marginBottom: '10px', color: '#495057' }}>📋 Requirements:</h4>
        <ul style={{ margin: '0', paddingLeft: '20px', fontSize: '14px', color: '#666' }}>
          <li>Username must be unique</li>
          <li>Password must be at least 6 characters</li>
          <li>All fields are required</li>
        </ul>
      </div>
    </div>
  );
};

export default Signup;
