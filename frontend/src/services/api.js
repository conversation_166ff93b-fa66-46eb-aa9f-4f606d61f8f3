import axios from 'axios';

// 创建 axios 实例
const api = axios.create({
  baseURL: 'http://localhost:8080',
  withCredentials: true, // 重要：允许发送 cookies
  headers: {
    'Content-Type': 'application/json',
  },
});

// 存储 access token
let accessToken = localStorage.getItem('access_token');

// 请求拦截器：自动添加 Authorization 头
api.interceptors.request.use(
  (config) => {
    if (accessToken) {
      config.headers.Authorization = `Bearer ${accessToken}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器：自动处理 token 刷新
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    // 如果是 401 错误且不是刷新 token 请求，尝试刷新 token
    if (error.response?.status === 401 && !originalRequest._retry && !originalRequest.url.includes('/refresh')) {
      originalRequest._retry = true;

      try {
        console.log('Access token expired, trying to refresh...');
        const refreshResponse = await api.post('/v1/auth/refresh');
        
        if (refreshResponse.status === 200) {
          const newAccessToken = refreshResponse.data.access_token;
          accessToken = newAccessToken;
          localStorage.setItem('access_token', newAccessToken);
          
          // 重试原请求
          originalRequest.headers.Authorization = `Bearer ${newAccessToken}`;
          return api(originalRequest);
        }
      } catch (refreshError) {
        console.log('Refresh token also expired, redirecting to login...');
        // Refresh token 也过期了，清除本地存储
        accessToken = null;
        localStorage.removeItem('access_token');
        // 可以在这里触发登出事件或重定向到登录页
        window.dispatchEvent(new CustomEvent('auth:logout'));
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);

// API 方法
export const authAPI = {
  // 登录
  login: async (username, password) => {
    const formData = new URLSearchParams();
    formData.append('username', username);
    formData.append('password', password);
    formData.append('grant_type', 'password');

    const response = await api.post('/v1/auth/login', formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });

    if (response.data.access_token) {
      accessToken = response.data.access_token;
      localStorage.setItem('access_token', accessToken);
    }

    return response.data;
  },

  // 注册
  signup: async (username, password) => {
    const response = await api.post('/v1/auth/signup', {
      username,
      password,
    });
    return response.data;
  },

  // 登出
  logout: async () => {
    try {
      await api.post('/v1/auth/logout');
    } catch (error) {
      console.log('Logout request failed, but clearing local storage anyway');
    } finally {
      // 无论如何都清除本地存储
      accessToken = null;
      localStorage.removeItem('access_token');
    }
  },

  // 获取当前用户信息（测试受保护的端点）
  getCurrentUser: async () => {
    const response = await api.get('/v1/auth/me');
    return response.data;
  },

  // 刷新 token
  refreshToken: async () => {
    const response = await api.post('/v1/auth/refresh');
    if (response.data.access_token) {
      accessToken = response.data.access_token;
      localStorage.setItem('access_token', accessToken);
    }
    return response.data;
  },

  // 验证 refresh token
  validateRefreshToken: async () => {
    const response = await api.get('/v1/auth/refresh/validate');
    return response.data;
  },

  // 获取 refresh token 信息
  getRefreshTokenInfo: async () => {
    const response = await api.get('/v1/auth/refresh/info');
    return response.data;
  },

  // 测试认证
  testAuth: async () => {
    const response = await api.get('/v1/auth/test-swagger-auth');
    return response.data;
  },
};

// 检查是否已登录
export const isAuthenticated = () => {
  return !!accessToken;
};

// 获取当前 access token
export const getAccessToken = () => {
  return accessToken;
};

// 设置 access token
export const setAccessToken = (token) => {
  accessToken = token;
  if (token) {
    localStorage.setItem('access_token', token);
  } else {
    localStorage.removeItem('access_token');
  }
};

export default api;
