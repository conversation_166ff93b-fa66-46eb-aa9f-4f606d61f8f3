import React, { useState } from 'react';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import Navbar from './components/Navbar';
import Login from './components/Login';
import Signup from './components/Signup';
import Dashboard from './components/Dashboard';

// 主应用内容组件
const AppContent = () => {
  const { isLoggedIn, loading } = useAuth();
  const [showSignup, setShowSignup] = useState(false);

  if (loading) {
    return (
      <>
        <Navbar />
        <div className="container">
          <div className="loading">
            <h2>🔄 Loading...</h2>
            <p>Checking authentication status...</p>
          </div>
        </div>
      </>
    );
  }

  if (isLoggedIn) {
    return (
      <>
        <Navbar />
        <Dashboard />
      </>
    );
  }

  return (
    <>
      <Navbar />
      <div className="container">
        <div style={{ maxWidth: '500px', margin: '0 auto' }}>
          {showSignup ? (
            <Signup onSwitchToLogin={() => setShowSignup(false)} />
          ) : (
            <Login onSwitchToSignup={() => setShowSignup(true)} />
          )}
        </div>
      </div>
    </>
  );
};

// 主应用组件
const App = () => {
  return (
    <AuthProvider>
      <div className="App">
        <AppContent />
        
        {/* 页脚信息 */}
        <footer style={{ 
          textAlign: 'center', 
          padding: '30px 20px', 
          color: '#666',
          borderTop: '1px solid #eee',
          marginTop: '50px'
        }}>
          <p>
            🔐 FastAPI Auth Demo - Demonstrating JWT Authentication with Refresh Tokens
          </p>
          <p style={{ fontSize: '14px', marginTop: '10px' }}>
            Features: Login, Signup, Logout, Automatic Token Refresh, Protected Routes
          </p>
        </footer>
      </div>
    </AuthProvider>
  );
};

export default App;
