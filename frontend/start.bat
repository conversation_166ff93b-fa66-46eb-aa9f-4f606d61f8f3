@echo off
echo 🚀 Starting FastAPI Auth Frontend Demo...
echo.
echo 📋 Prerequisites:
echo 1. Node.js should be installed
echo 2. FastAPI backend should be running on http://localhost:8080
echo.

REM 检查是否已安装依赖
if not exist "node_modules" (
    echo 📦 Installing dependencies...
    npm install
    echo.
)

echo 🌐 Starting React development server...
echo The app will open at http://localhost:3000
echo.
echo 💡 Test Account:
echo Username: heygo
echo Password: Heytime01!
echo.

npm start
