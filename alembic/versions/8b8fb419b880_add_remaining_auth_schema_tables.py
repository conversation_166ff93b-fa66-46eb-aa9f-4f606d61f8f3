"""Add remaining auth schema tables

Revision ID: 8b8fb419b880
Revises: 8067d43d3c9b
Create Date: 2025-07-31 17:21:54.275678

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '8b8fb419b880'
down_revision: Union[str, Sequence[str], None] = '8067d43d3c9b'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('api_permissions',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('api_path', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('required_scopes', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('category', sa.String(length=50), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    schema='auth'
    )
    op.create_index(op.f('ix_auth_api_permissions_api_path'), 'api_permissions', ['api_path'], unique=True, schema='auth')
    op.create_index(op.f('ix_auth_api_permissions_id'), 'api_permissions', ['id'], unique=False, schema='auth')
    op.create_table('associate_roles_permissions',
    sa.Column('role_name', sa.Enum('admin', 'user', 'test', name='roletype', native_enum=False), nullable=False),
    sa.Column('permission_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['permission_id'], ['auth.permissions.id'], ),
    sa.ForeignKeyConstraint(['role_name'], ['auth.roles.name'], ),
    sa.PrimaryKeyConstraint('role_name', 'permission_id'),
    schema='auth'
    )
    op.create_table('associate_users_roles',
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('role_name', sa.Enum('admin', 'user', 'test', name='roletype', native_enum=False), nullable=False),
    sa.ForeignKeyConstraint(['role_name'], ['auth.roles.name'], ),
    sa.ForeignKeyConstraint(['user_id'], ['auth.users.id'], ),
    sa.PrimaryKeyConstraint('user_id', 'role_name'),
    schema='auth'
    )
    op.create_table('user_tokens',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('token_name', sa.String(length=255), nullable=False),
    sa.Column('expires_at', sa.DateTime(), nullable=False),
    sa.Column('user_id', sa.Integer(), nullable=False),
    sa.Column('token', sa.String(length=255), nullable=False),
    sa.Column('permissions', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['auth.users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    schema='auth'
    )
    op.create_index(op.f('ix_auth_user_tokens_id'), 'user_tokens', ['id'], unique=False, schema='auth')
    op.create_index(op.f('ix_auth_user_tokens_token'), 'user_tokens', ['token'], unique=True, schema='auth')
    op.create_index(op.f('ix_auth_user_tokens_token_name'), 'user_tokens', ['token_name'], unique=True, schema='auth')
    op.create_table('associate_token_api_permissions',
    sa.Column('token_id', sa.Integer(), nullable=False),
    sa.Column('api_permission_id', sa.Integer(), nullable=False),
    sa.ForeignKeyConstraint(['api_permission_id'], ['auth.api_permissions.id'], ),
    sa.ForeignKeyConstraint(['token_id'], ['auth.user_tokens.id'], ),
    sa.PrimaryKeyConstraint('token_id', 'api_permission_id'),
    schema='auth'
    )
    op.drop_table('associate_token_api_permissions')
    op.drop_index(op.f('ix_api_permissions_api_path'), table_name='api_permissions')
    op.drop_index(op.f('ix_api_permissions_id'), table_name='api_permissions')
    op.drop_table('api_permissions')
    op.drop_index(op.f('ix_user_tokens_id'), table_name='user_tokens')
    op.drop_index(op.f('ix_user_tokens_token'), table_name='user_tokens')
    op.drop_index(op.f('ix_user_tokens_token_name'), table_name='user_tokens')
    op.drop_table('user_tokens')
    op.drop_index(op.f('ix_roles_id'), table_name='roles')
    op.drop_index(op.f('ix_roles_name'), table_name='roles')
    op.drop_table('roles')
    op.drop_table('associate_users_roles')
    op.drop_index(op.f('ix_users_id'), table_name='users')
    op.drop_index(op.f('ix_users_username'), table_name='users')
    op.drop_table('users')
    op.drop_table('associate_roles_permissions')
    op.drop_index(op.f('ix_permissions_id'), table_name='permissions')
    op.drop_index(op.f('ix_permissions_name'), table_name='permissions')
    op.drop_table('permissions')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('permissions',
    sa.Column('id', sa.INTEGER(), server_default=sa.text("nextval('permissions_id_seq'::regclass)"), autoincrement=True, nullable=False),
    sa.Column('name', sa.VARCHAR(length=22), autoincrement=False, nullable=False),
    sa.Column('description', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.PrimaryKeyConstraint('id', name='permissions_pkey'),
    postgresql_ignore_search_path=False
    )
    op.create_index(op.f('ix_permissions_name'), 'permissions', ['name'], unique=True)
    op.create_index(op.f('ix_permissions_id'), 'permissions', ['id'], unique=False)
    op.create_table('associate_roles_permissions',
    sa.Column('role_name', sa.VARCHAR(length=5), autoincrement=False, nullable=False),
    sa.Column('permission_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['permission_id'], ['permissions.id'], name=op.f('associate_roles_permissions_permission_id_fkey')),
    sa.ForeignKeyConstraint(['role_name'], ['roles.name'], name=op.f('associate_roles_permissions_role_name_fkey')),
    sa.PrimaryKeyConstraint('role_name', 'permission_id', name=op.f('associate_roles_permissions_pkey'))
    )
    op.create_table('users',
    sa.Column('id', sa.INTEGER(), server_default=sa.text("nextval('users_id_seq'::regclass)"), autoincrement=True, nullable=False),
    sa.Column('username', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('password', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.PrimaryKeyConstraint('id', name='users_pkey'),
    postgresql_ignore_search_path=False
    )
    op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=True)
    op.create_index(op.f('ix_users_id'), 'users', ['id'], unique=False)
    op.create_table('associate_users_roles',
    sa.Column('user_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('role_name', sa.VARCHAR(length=5), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['role_name'], ['roles.name'], name=op.f('associate_users_roles_role_name_fkey')),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], name=op.f('associate_users_roles_user_id_fkey')),
    sa.PrimaryKeyConstraint('user_id', 'role_name', name=op.f('associate_users_roles_pkey'))
    )
    op.create_table('roles',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('name', sa.VARCHAR(length=5), autoincrement=False, nullable=False),
    sa.Column('description', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.PrimaryKeyConstraint('id', name=op.f('roles_pkey'))
    )
    op.create_index(op.f('ix_roles_name'), 'roles', ['name'], unique=True)
    op.create_index(op.f('ix_roles_id'), 'roles', ['id'], unique=False)
    op.create_table('user_tokens',
    sa.Column('id', sa.INTEGER(), server_default=sa.text("nextval('user_tokens_id_seq'::regclass)"), autoincrement=True, nullable=False),
    sa.Column('token_name', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('expires_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.Column('user_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('token', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('permissions', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True),
    sa.Column('is_active', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], name='user_tokens_user_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='user_tokens_pkey'),
    postgresql_ignore_search_path=False
    )
    op.create_index(op.f('ix_user_tokens_token_name'), 'user_tokens', ['token_name'], unique=True)
    op.create_index(op.f('ix_user_tokens_token'), 'user_tokens', ['token'], unique=True)
    op.create_index(op.f('ix_user_tokens_id'), 'user_tokens', ['id'], unique=False)
    op.create_table('api_permissions',
    sa.Column('id', sa.INTEGER(), server_default=sa.text("nextval('api_permissions_id_seq'::regclass)"), autoincrement=True, nullable=False),
    sa.Column('api_path', sa.VARCHAR(length=255), autoincrement=False, nullable=False),
    sa.Column('description', sa.TEXT(), autoincrement=False, nullable=True),
    sa.Column('required_scopes', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=False),
    sa.Column('is_active', sa.BOOLEAN(), autoincrement=False, nullable=False),
    sa.Column('category', sa.VARCHAR(length=50), autoincrement=False, nullable=False),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=False),
    sa.PrimaryKeyConstraint('id', name='api_permissions_pkey'),
    postgresql_ignore_search_path=False
    )
    op.create_index(op.f('ix_api_permissions_id'), 'api_permissions', ['id'], unique=False)
    op.create_index(op.f('ix_api_permissions_api_path'), 'api_permissions', ['api_path'], unique=True)
    op.create_table('associate_token_api_permissions',
    sa.Column('token_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('api_permission_id', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['api_permission_id'], ['api_permissions.id'], name=op.f('associate_token_api_permissions_api_permission_id_fkey')),
    sa.ForeignKeyConstraint(['token_id'], ['user_tokens.id'], name=op.f('associate_token_api_permissions_token_id_fkey')),
    sa.PrimaryKeyConstraint('token_id', 'api_permission_id', name=op.f('associate_token_api_permissions_pkey'))
    )
    op.drop_table('associate_token_api_permissions', schema='auth')
    op.drop_index(op.f('ix_auth_user_tokens_token_name'), table_name='user_tokens', schema='auth')
    op.drop_index(op.f('ix_auth_user_tokens_token'), table_name='user_tokens', schema='auth')
    op.drop_index(op.f('ix_auth_user_tokens_id'), table_name='user_tokens', schema='auth')
    op.drop_table('user_tokens', schema='auth')
    op.drop_table('associate_users_roles', schema='auth')
    op.drop_table('associate_roles_permissions', schema='auth')
    op.drop_index(op.f('ix_auth_api_permissions_id'), table_name='api_permissions', schema='auth')
    op.drop_index(op.f('ix_auth_api_permissions_api_path'), table_name='api_permissions', schema='auth')
    op.drop_table('api_permissions', schema='auth')
    # ### end Alembic commands ###
