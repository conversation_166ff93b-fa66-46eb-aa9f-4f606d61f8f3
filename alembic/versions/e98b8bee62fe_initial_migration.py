"""initial_migration

Revision ID: e98b8bee62fe
Revises: 
Create Date: 2025-07-10 10:03:52.503138

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'e98b8bee62fe'
down_revision: Union[str, Sequence[str], None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###

    # SQLite doesn't support ALTER COLUMN for changing data types
    # We'll skip the permissions table column type change for now
    # The enum constraint will be handled at the application level

    # Add new columns to user_tokens table
    op.add_column('user_tokens', sa.Column('token_name', sa.String(length=255), nullable=False))
    op.add_column('user_tokens', sa.Column('expires_at', sa.DateTime(), nullable=False))
    op.add_column('user_tokens', sa.Column('permissions', sa.Text(), nullable=True))  # Use Text instead of JsonList for SQLite compatibility
    op.add_column('user_tokens', sa.Column('is_active', sa.Boolean(), nullable=False))
    op.create_index(op.f('ix_user_tokens_token_name'), 'user_tokens', ['token_name'], unique=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_user_tokens_token_name'), table_name='user_tokens')
    op.drop_column('user_tokens', 'is_active')
    op.drop_column('user_tokens', 'permissions')
    op.drop_column('user_tokens', 'expires_at')
    op.drop_column('user_tokens', 'token_name')

    # SQLite doesn't support ALTER COLUMN for changing data types
    # Skip the permissions table column type change
    # ### end Alembic commands ###
