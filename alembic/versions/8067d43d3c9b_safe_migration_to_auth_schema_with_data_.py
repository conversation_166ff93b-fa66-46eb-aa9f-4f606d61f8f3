"""Safe migration to auth schema with data preservation

Revision ID: 8067d43d3c9b
Revises: e98b8bee62fe
Create Date: 2025-07-31 17:19:32.830547

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '8067d43d3c9b'
down_revision: Union[str, Sequence[str], None] = 'e98b8bee62fe'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema - migrate tables to auth schema with data preservation."""

    # 1. 创建 auth schema
    op.execute("CREATE SCHEMA IF NOT EXISTS auth")

    # 2. 在 auth schema 中创建新表结构
    # Users table
    op.create_table('users',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('username', sa.String(length=255), nullable=False),
        sa.Column('password', sa.String(length=255), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.PrimaryKeyConstraint('id'),
        schema='auth'
    )
    op.create_index('ix_auth_users_id', 'users', ['id'], schema='auth')
    op.create_index('ix_auth_users_username', 'users', ['username'], unique=True, schema='auth')

    # Roles table
    op.create_table('roles',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('name', sa.Enum('ADMIN', 'USER', 'GUEST', name='roletype', native_enum=False), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.PrimaryKeyConstraint('id'),
        schema='auth'
    )
    op.create_index('ix_auth_roles_id', 'roles', ['id'], schema='auth')
    op.create_index('ix_auth_roles_name', 'roles', ['name'], unique=True, schema='auth')

    # Permissions table
    op.create_table('permissions',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('name', sa.Enum('manage_users', 'manage_roles', 'manage_permissions', 'manage_tokens', 'manage_api_permissions', 'download_file', 'upload_file', 'write_database', 'read_database', 'delete_record', 'create_user', 'update_profile', 'modify_settings', 'create_record', 'administrator', name='permissiontype', native_enum=False), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.PrimaryKeyConstraint('id'),
        schema='auth'
    )
    op.create_index('ix_auth_permissions_id', 'permissions', ['id'], schema='auth')
    op.create_index('ix_auth_permissions_name', 'permissions', ['name'], unique=True, schema='auth')


def downgrade() -> None:
    """Downgrade schema."""
    # 删除 auth schema 中的表
    op.drop_table('permissions', schema='auth')
    op.drop_table('roles', schema='auth')
    op.drop_table('users', schema='auth')

    # 删除 auth schema（如果为空）
    op.execute("DROP SCHEMA IF EXISTS auth")
