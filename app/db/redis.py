import redis.asyncio as redis
from redis.exceptions import RedisError, ConnectionError, TimeoutError
from loguru import logger
from app.config.config import settings



# 配置 Redis 连接池
try:
    redis_pool = redis.ConnectionPool(**settings.REDIS_CONFIG.model_dump())
    logger.info("Redis connection pool created successfully")
except Exception as e:
    logger.error(f"Failed to create Redis connection pool: {e}")
    redis_pool = None

async def init_redis():
    """
    初始化 Redis 连接

    Returns:
        redis.Redis: Redis 连接实例，连接失败时返回 None
    """
    if redis_pool is None:
        logger.error("Redis connection pool not initialized")
        return None

    try:
        conn = redis.Redis(connection_pool=redis_pool)

        # 检查连接是否成功
        pong = await conn.ping()
        if pong:
            logger.info("Redis connection successful")
            return conn
        else:
            logger.error("Redis ping failed")
            return None

    except (ConnectionError, TimeoutError) as e:
        logger.error(f"Redis connection/timeout error: {e}")
        return None
    except RedisError as e:
        logger.error(f"Redis error: {e}")
        return None
    except Exception as e:
        logger.error(f"Unexpected Redis connection error: {e}")
        return None


async def close_redis(redis_client):
    """
    关闭 Redis 连接

    Args:
        redis_client: Redis 连接实例
    """
    if redis_client:
        try:
            await redis_client.close()
            logger.info("Redis connection closed")
        except Exception as e:
            logger.error(f"Error closing Redis connection: {e}")