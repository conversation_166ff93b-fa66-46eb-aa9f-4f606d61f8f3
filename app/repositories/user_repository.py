from typing import Optional, Dict, Any
from sqlalchemy import select, update, delete ,and_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload, selectinload, contains_eager
from loguru import logger

from app.models.user import User, Role, Permission ,UserApiToken ,ApiPermission
from app.repositories.base_repository import BaseRepository
from app.core.utils import Utils


class UserRepository(BaseRepository[User]):
    """用户数据仓储类"""

    def __init__(self, db: AsyncSession):
        """
        初始化仓储

        Args:
            db: 异步数据库会话
        """
        super().__init__(db, User)

    # 用户特有的方法
    async def get_one_by_username(self, username: str) -> Optional[User]:
        """
        通过用户名获取用户

        Args:
            username: 用户名

        Returns:
            Optional[User]: 找到的用户，未找到则返回 None
        """
        return await self.find_one_by_field("username", username)

    async def get_one_by_username_with_roles(self, username: str) -> Optional[User]:
        """
        通过用户名获取用户及其角色

        Args:
            username: 用户名

        Returns:
            Optional[User]: 找到的用户，未找到则返回 None
        """
        stmt = select(User).where(User.username == username).options(
            selectinload(User.roles)
        )
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()


class RoleRepository(BaseRepository[Role]):
    """角色数据仓储类"""

    def __init__(self, db: AsyncSession):
        """
        初始化仓储

        Args:
            db: 异步数据库会话
        """
        super().__init__(db, Role)

    # 角色特有的方法
    async def get_one_by_name(self, name: str) -> Optional[Role]:
        """
        通过角色名获取角色

        Args:
            name: 角色名

        Returns:
            Optional[Role]: 找到的角色，未找到则返回 None
        """
        return await self.find_one_by_field("name", name)

    async def get_one_by_name_with_permissions(self, role_name: str) -> Optional[Role]:
        """
        通过角色名获取角色及其权限

        Args:
            role_name: 角色名

        Returns:
            Optional[Role]: 找到的角色，未找到则返回 None
        """
        stmt = select(Role).where(Role.name == role_name).options(
            selectinload(Role.permissions)
        )
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()





class PermissionRepository(BaseRepository[Permission]):
    """权限数据仓储类"""

    def __init__(self, db: AsyncSession):
        """
        初始化仓储

        Args:
            db: 异步数据库会话
        """
        super().__init__(db, Permission)

    # 权限特有的方法
    async def get_one_by_name(self, name: str) -> Optional[Permission]:
        """
        通过权限名获取权限

        Args:
            name: 权限名

        Returns:
            Optional[Permission]: 找到的权限，未找到则返回 None
        """
        return await self.find_one_by_field("name", name)

    async def get_by_id(self, id: int) -> Optional[Permission]:
        """
        通过ID获取权限

        Args:
            id: 权限ID

        Returns:
            Optional[Permission]: 找到的权限，未找到则返回 None
        """
        return await self.find_one_by_field("id", id)


class UserTokenRepository(BaseRepository[UserApiToken]):
    """用户令牌数据仓储类"""

    def __init__(self, db: AsyncSession):
        """
        初始化仓储

        Args:
            db: 异步数据库会话
        """
        super().__init__(db, UserApiToken)

class ApiPermissionRepository(BaseRepository[ApiPermission]):
    """用户令牌数据仓储类"""
    def __init__(self, db: AsyncSession):
        """
        初始化仓储

        Args:
            db: 异步数据库会话
        """
        super().__init__(db, ApiPermission)

    async def get_all_api_permissions(self) -> list[ApiPermission]:
        """获取所有API权限配置"""
        result = await self.db.execute(select(self.model_class).where(self.model_class.is_active))
        return list(result.scalars().all())

    async def get_api_permission_by_path(self, api_path: str) -> Optional[ApiPermission]:
        """根据API路径获取权限配置"""
        result = await self.db.execute(
            select(self.model_class).where(
                and_(self.model_class.api_path == api_path, self.model_class.is_active)
            )
        )
        return result.scalar_one_or_none()

    async def create_api_permission(self, perm_config: Dict[str, Any]) -> ApiPermission:
        """创建API权限配置"""
        api_permission = self.model_class(
            api_path=perm_config['api_path'],
            description=perm_config['description'],
            required_scopes=perm_config['required_scopes'],
            is_active=perm_config['is_active']
        )
        self.db.add(api_permission)
        return api_permission


    async def update_api_permission(self, api_path: str,  perm_config: Dict[str, Any]) -> Optional[ApiPermission]:
        """更新API权限配置"""

        stmt = select(self.model_class).where(self.model_class.api_path == api_path)
        result = await self.db.execute(stmt)
        api_permission = result.scalar_one_or_none()


        if api_permission is None:
            logger.warning(f"未找到api_path为 {api_path} 的API记录，无法更新")
            return None

        # 记录更新前的值
        logger.debug(f"更新前的实体: {Utils.shorten_item(api_permission,50)}")

        # 跟踪更新的字段
        updated_fields = {}

        # 使用对象属性方式更新，这样可以触发SQLAlchemy的事件和属性设置逻辑
        for key, value in perm_config.items():
            if hasattr(api_permission, key):
                old_value = getattr(api_permission, key)
                if old_value != value:  # 只记录实际变化的字段
                    updated_fields[key] = {"old": Utils.shorten_item(old_value, 30),
                                           "new": Utils.shorten_item(value, 30)}
                    setattr(api_permission, key, value)


        # 只记录实际更新的字段
        if updated_fields:
            logger.success(f"更新的字段: {updated_fields}")

        else:
            logger.info("没有字段发生实际更新")

        return api_permission




    async def delete_api_permission(self, api_path: str) -> bool:
        """更新API权限配置"""

        result = await self.db.execute(
            select(self.model_class).where(self.model_class.api_path == api_path)
        )

        api_permission = result.scalar_one_or_none()
        if api_permission:
            await self.db.delete(api_permission)
            return True
        return False

    async def mark_api_permission_is_active(self, api_path: str, is_active: bool) :
        """更新API权限配置"""

        result = await self.db.execute(
            select(self.model_class).where(self.model_class.api_path == api_path)
        )

        api_permission = result.scalar_one_or_none()
        if api_permission:
            api_permission.is_active = is_active
            return {"success": True, "message": f"API权限配置已更新为{is_active}"}
        return {"success": False, "message": f"未找到api_path为 {api_path} 的API记录，无法更新"}

    async def toggle_api_permission_is_active(self, api_path: str) :
        """更新API权限配置"""

        result = await self.db.execute(
            select(self.model_class).where(self.model_class.api_path == api_path)
        )
        api_permission = result.scalar_one_or_none()

        if api_permission:
            api_permission.is_active = not api_permission.is_active
            return {"success": True, "message": f"API权限配置已更新为{not api_permission.is_active}"}

        return {"success": False, "message": f"未找到api_path为 {api_path} 的API记录，无法更新"}




