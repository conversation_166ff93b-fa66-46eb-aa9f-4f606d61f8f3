# config/api_permissions.yml
api_permissions:
  # 用户管理相关
  user_management:
    - path: "/auth/remove_roles_from_user"
      description: "从用户移除角色"
      required_scopes: ["manage_users", "delete_record"]
      is_active: true

    - path: "/auth/assign_roles_to_user"
      description: "为用户分配角色"
      required_scopes: ["manage_users", "create_record"]
      is_active: true

    - path: "/auth/get_user_roles"
      description: "获取用户角色"
      required_scopes: ["manage_users", "read_record"]
      is_active: true

  # 角色管理相关
  role_management:
    - path: "/auth/create_role"
      description: "创建角色"
      required_scopes: ["manage_roles", "create_record"]
      is_active: true

    - path: "/auth/delete_role"
      description: "删除角色"
      required_scopes: ["manage_roles", "delete_record"]
      is_active: true

  # 权限管理相关
  permission_management:
    - path: "/auth/create_permission"
      description: "创建权限"
      required_scopes: ["manage_permissions", "create_record"]
      is_active: true

    - path: "/auth/assign_permission_to_role"
      description: "为角色分配权限"
      required_scopes: ["manage_permissions", "create_record"]
      is_active: true

    - path: "/auth/remove_permission_from_role"
      description: "从角色移除权限"
      required_scopes: ["manage_permissions", "delete_record"]
      is_active: true

  # Token管理相关
  token_management:
    - path: "/auth/create_token_with_scopes"
      description: "创建带权限范围的令牌"
      required_scopes: ["manage_tokens", "create_record"]
      is_active: true

    - path: "/auth/revoke_token"
      description: "撤销令牌"
      required_scopes: ["manage_tokens", "delete_record"]
      is_active: true

  # 公开接口（无需权限）
  public_apis:
    - path: "/auth/login"
      description: "用户登录"
      required_scopes: []
      is_active: true

    - path: "/auth/register"
      description: "用户注册"
      required_scopes: []
      is_active: true

    - path: "/auth/get_current_user_permissions"
      description: "获取当前用户权限"
      required_scopes: []
      is_active: true

  # API权限管理后台
  api_permissions_management:
    - path: "/admin/api_permissions/sync_api_permissions"
      description: "同步权限配置"
      required_scopes: ["manage_api_permissions"]
      is_active: true

    - path: "/admin/api_permissions/validate_api_permissions_yml"
      description: "验证权限配置"
      required_scopes: ["manage_api_permissions"]
      is_active: true

    - path: "/admin/api_permissions/export_db_to_yaml"
      description: "导出权限配置"
      required_scopes: ["manage_api_permissions"]
      is_active: true

    - path: "/admin/api_permissions/get_all_api_permissions"
      description: "获取权限列表"
      required_scopes: ["manage_api_permissions", "read_record"]
      is_active: true

    - path: "/admin/api_permissions/toggle_api_permission"
      description: "切换权限状态"
      required_scopes: ["manage_api_permissions","administrator"]
      is_active: true