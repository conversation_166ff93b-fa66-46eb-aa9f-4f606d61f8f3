"""
应用启动时的初始化任务
"""
from loguru import logger
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.sync_api_permission import sync_api_permissions, validate_permission_config
from app.core.deps import get_async_session


async def sync_permissions_on_startup():
    """应用启动时同步权限配置"""
    try:
        # 先验证配置文件
        logger.info("验证权限配置文件...")
        errors = await validate_permission_config()
        
        if errors:
            logger.error("权限配置文件验证失败:")
            for error in errors:
                logger.error(f"  - {error}")
            raise Exception("权限配置文件验证失败")
        
        logger.info("权限配置文件验证通过")
        
        # 同步到数据库
        logger.info("同步权限配置到数据库...")
        async for db in get_async_session():
            stats = await sync_api_permissions(db)
            logger.info(f"权限同步完成: 创建{stats['created']}个, 更新{stats['updated']}个, 停用{stats['deactivated']}个")
            break  # 只需要第一个数据库会话
            
    except Exception as e:
        logger.error(f"启动时权限同步失败: {e}")
        # 根据需要决定是否要让应用启动失败
        # raise  # 如果权限配置是必需的，可以取消注释这行


async def initialize_app():
    """应用初始化"""
    logger.info("开始应用初始化...")
    
    # 同步权限配置
    await sync_permissions_on_startup()
    
    # 可以在这里添加其他初始化任务
    # await init_other_components()
    
    logger.info("应用初始化完成")
