from typing import Annotated

from fastapi import Depends, HTTPException,Request ,status

from loguru import logger


from app.core.auth import authenticate_token, TokenService
from app.schemas.user import AuthContext
from app.core.enums import TokenType
from app.repositories.token_repository import RefreshTokenRepository, get_refresh_token_repository
from app.core.deps import AsyncSessionDep


# ===== 权限检查依赖工厂 =====
class RequireApiPermission:
    """
    统一的API权限检查依赖工厂

    自动从 api_permissions.yml 配置文件中获取当前API路径所需的权限，
    并根据token类型进行相应的权限检查：
    - Access Token: 检查用户权限（permissions）
    - API Token: 检查token权限范围（scopes）
    """

    def __init__(self, api_path: str = None):
        """
        初始化权限检查器

        Args:
            api_path: 可选的API路径，如果不提供则从请求中自动获取
        """
        self.api_path = api_path

    def __call__(self):
        """
        返回一个依赖函数，用于权限检查

        Returns:
            function: FastAPI依赖函数
        """
        async def check_permission(
            request: Request,
            db: AsyncSessionDep,
            token_repo: RefreshTokenRepository = Depends(get_refresh_token_repository),
            auth_context: AuthContext = Depends(authenticate_token)
        ) -> AuthContext:
            """
            执行权限检查

            Args:
                request: FastAPI请求对象
                db: 数据库会话
                token_repo: Refresh Token 仓储
                auth_context: 认证上下文

            Returns:
                AuthContext: 验证通过的认证上下文

            Raises:
                HTTPException: 权限不足时抛出403错误
            """


            # 获取API路径
            current_api_path = self.api_path or request.url.path

            # 从数据库获取API所需权限
            required_scopes = await TokenService.get_api_scopes(current_api_path, db ,token_repo)
            logger.info(f"API {current_api_path} 需要权限: {required_scopes}")

            # 如果没有配置权限要求，直接通过
            if not required_scopes:
                return auth_context

            # 根据token类型进行权限检查
            if auth_context.token_type == TokenType.ACCESS:
                # Access Token: 检查用户权限
                missing_permissions = set(required_scopes) - set(auth_context.permissions)
                if missing_permissions:
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail=f"Missing permissions: {', '.join(missing_permissions)}"
                    )

            elif auth_context.token_type == TokenType.API:
                # API Token: 检查scopes
                if not auth_context.scopes:
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail="API token missing scopes"
                    )

                # todo 这里还不是
                missing_scopes = set(required_scopes) - set(auth_context.scopes)
                if missing_scopes:
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail=f"Missing scopes: {', '.join(missing_scopes)}"
                    )

            return auth_context

        return check_permission

