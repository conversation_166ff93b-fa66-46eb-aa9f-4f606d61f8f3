"""
API权限配置同步工具

从YAML配置文件读取API权限配置，并同步到数据库中
"""
import yaml
import json
from pathlib import Path
from typing import   Any
from loguru import logger
from sqlalchemy.ext.asyncio import AsyncSession

from app.repositories.user_repository import ApiPermissionRepository


class ApiPermissionConfigLoader:
    """权限配置加载器"""
    
    def __init__(self, config_path: str = "app/config/api_permissions.yml"):
        self.config_path = Path(config_path)
    
    def load_config(self) -> dict[str, Any]:
        """加载配置文件"""
        if not self.config_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {self.config_path}")
        
        try:
            with open(self.config_path, 'r', encoding='utf-8') as file:
                if self.config_path.suffix.lower() in ['.yml', '.yaml']:
                    return yaml.safe_load(file)
                elif self.config_path.suffix.lower() == '.json':
                    return json.load(file)
                else:
                    raise ValueError(f"不支持的配置文件格式: {self.config_path.suffix}")
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            raise
    
    def parse_permissions(self) -> list[dict[str, Any]]:
        """解析权限配置"""
        config = self.load_config()
        permissions = []
        
        api_permissions = config.get('api_permissions', {})
        
        for category, apis in api_permissions.items():
            for api_config in apis:
                permission_data = {
                    'api_path': api_config['path'],
                    'description': api_config.get('description', ''),
                    'required_scopes': api_config.get('required_scopes', []),
                    'is_active': api_config.get('is_active', True),
                    'category': category  # 添加分类信息
                }
                permissions.append(permission_data)
        
        return permissions


class ApiPermissionSyncService:
    """权限同步服务"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.repo = ApiPermissionRepository(db)
        self.loader = ApiPermissionConfigLoader()

    @classmethod
    async def sync_yml_to_db(cls, db: AsyncSession) -> dict[str, int]:
        """同步权限配置到数据库"""
        try:
            service = cls(db)
            permissions_config = service.loader.parse_permissions()
            existing_permissions = await service.repo.get_all_api_permissions()
            existing_paths = {perm.api_path: perm for perm in existing_permissions}

            stats = {'created': 0, 'updated': 0, 'deactivated': 0, 'skipped': 0}

            # 获取所有路径集合
            config_paths = {perm['api_path'] for perm in permissions_config}
            existing_active_paths = {path for path, perm in existing_paths.items() if perm.is_active}

            # 需要创建的权限：在YAML中但不在数据库中
            paths_to_create = config_paths - existing_paths.keys()

            # 需要更新的权限：在YAML中也在数据库中
            paths_to_update = config_paths & existing_paths.keys()

            # 需要停用的权限：在数据库中活跃但不在YAML中
            paths_to_deactivate = existing_active_paths - config_paths

            # 创建权限配置的字典，便于查找
            config_dict = {perm['api_path']: perm for perm in permissions_config}

            # 批量处理创建
            for api_path in paths_to_create:
                await service.repo.create_api_permission(config_dict[api_path])
                stats['created'] += 1

            # 批量处理更新
            for api_path in paths_to_update:
                updated = await service.repo.update_api_permission(existing_paths[api_path].api_path, config_dict[api_path])
                stats['updated' if updated else 'skipped'] += 1

            # 批量处理停用
            for api_path in paths_to_deactivate:
                await service.repo.mark_api_permission_is_active(existing_paths[api_path].api_path, False)
                stats['deactivated'] += 1
                logger.warning(f"API权限已停用: {api_path}")

            await service.db.commit()
            return stats

        except Exception as e:
            logger.error(f"权限同步失败: {e}")
            raise

    @classmethod
    async def validate_config(cls) -> list[str]:
        """验证配置文件"""
        errors = []
        loader = ApiPermissionConfigLoader()
        try:
            permissions_config = loader.parse_permissions()

            # 检查重复的API路径
            paths = [perm['api_path'] for perm in permissions_config]
            duplicates = set([path for path in paths if paths.count(path) > 1])
            if duplicates:
                errors.append(f"发现重复的API路径: {duplicates}")

            # 检查必需字段
            for perm in permissions_config:
                if not perm.get('api_path'):
                    errors.append(f"API路径不能为空: {perm}")

                if not isinstance(perm.get('required_scopes', []), list):
                    errors.append(f"required_scopes必须是列表: {perm['api_path']}")

        except Exception as e:
            errors.append(f"配置文件格式错误: {e}")

        return errors

    @classmethod
    async def export_db_to_yaml(cls, db: AsyncSession, output_path: str = "app/config/api_permissions_export.yml") -> None:
        """将数据库中的权限配置导出为YAML文件"""

        try:
            service = cls(db)
            permissions = await service.repo.get_all_api_permissions()

            # 按数据库中的category字段组织数据
            categories = {}
            for perm in permissions:
                # 直接使用数据库中的category字段
                category = perm.category if perm.category else 'uncategorized'

                if category not in categories:
                    categories[category] = []

                categories[category].append({
                    'path': perm.api_path,
                    'description': perm.description,
                    'required_scopes': perm.required_scopes,
                    'is_active': perm.is_active
                })

            # 构建YAML结构
            yaml_data = {'api_permissions': categories}

            # 写入文件
            output_file = Path(output_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)

            with open(output_file, 'w', encoding='utf-8') as file:
                yaml.dump(yaml_data, file, default_flow_style=False, allow_unicode=True, indent=2)

            logger.info(f"权限配置已导出到: {output_path}")

        except Exception as e:
            logger.error(f"导出权限配置失败: {e}")
            raise

