from fastapi import APIRouter, Depends, HTTPException, status, Security
from loguru import logger

from app.core.auth import check_user_permission
from app.core.deps import AsyncSessionDep
from app.models import Permission, Role
from app.core.enums import RoleType, PermissionType
from app.schemas.user import PermissionCreate
from app.repositories.user_repository import PermissionRepository, RoleRepository

async def init_roles( db: AsyncSessionDep):

    try:
        role_repo = RoleRepository(db)

        exist_roles_entity = await role_repo.get_all()
        exist_roles = [role.name.value for role in exist_roles_entity]  # 获取枚举的值

        stats = {'role_created': 0, 'role_deleted': 0, 'skipped': 0}

        # 需要创建的角色 - 保持为 RoleType 枚举
        roles_to_create = [role for role in RoleType if role.value not in exist_roles]

        # 需要删除的角色 - exist_roles 是字符串列表，需要与 RoleType 的值比较
        valid_role_values = [role.value for role in RoleType]
        roles_to_delete = [role for role in exist_roles if role not in valid_role_values]

        # 批量创建角色
        for role_type in roles_to_create:
            role_to_add = Role(name=role_type, description=role_type.description)
            await role_repo.create_entity(role_to_add)
            stats['role_created'] += 1
            logger.debug(f"创建角色: {role_type.value} - {role_type.description}")
            
        # 批量删除角色
        for role in roles_to_delete:
            role_to_delete = await role_repo.get_one_by_name(role)
            if role_to_delete:
                await role_repo.delete(role_to_delete.id)
                stats['role_deleted'] += 1
                logger.debug(f"删除角色: {role}")

        # 提交所有更改
        await db.commit()

        logger.success(f"角色初始化完成 - 角色 创建: {stats['role_created']}, 删除: {stats['role_deleted']}；")
        return stats

    except Exception as e:
        logger.error(f"初始化角色失败: {e}")
        await db.rollback()
        raise e


async def init_permissions(db: AsyncSessionDep):
    try:

        permission_repo = PermissionRepository(db)
        exist_permissions_entity = await permission_repo.get_all()
        exist_permissions = [permission.name.value for permission in exist_permissions_entity]  # 获取枚举的值

        stats = {'permission_created': 0, 'permission_updated': 0, 'permission_deleted': 0, 'skipped': 0}

        # 需要创建的权限 -保持为 PermissionType 枚举
        permissions_to_create = [perm for perm in PermissionType if perm.value not in exist_permissions]

        # 需要更新的权限 - exist_permissions 是字符串列表，需要与 PermissionType 的值比较
        permissions_to_update = [perm for perm in exist_permissions if perm in [perm.value for perm in PermissionType]]

        # 需要删除的权限 - exist_permissions 是字符串列表，需要与 PermissionType 的值比较
        valid_permission_values = [perm.value for perm in PermissionType]
        permissions_to_delete = [perm for perm in exist_permissions if perm not in valid_permission_values]

        # 批量创建权限
        for permission_type in permissions_to_create:
            permission_to_add = Permission(name=permission_type, description=permission_type.description)
            await permission_repo.create_entity(permission_to_add)
            stats['permission_created'] += 1
            logger.debug(f"创建权限: {permission_type.value} - {permission_type.description}")

        # 批量修改权限
        for perm in permissions_to_update:
            perm_to_update = await permission_repo.get_one_by_name(perm)
            if perm_to_update:
                perm_to_update.name = perm
                perm_to_update.description = PermissionType(perm).description
                await permission_repo.update_entity(perm_to_update)
                stats['permission_updated'] += 1
                logger.debug(f"更新权限: {perm_to_update.name} - {perm_to_update.description}")


        # 批量删除权限
        for perm in permissions_to_delete:
            perm_to_delete = await permission_repo.get_one_by_name(perm)
            if perm_to_delete:
                await permission_repo.delete(perm_to_delete.id)
                stats['permission_deleted'] += 1
                logger.debug(f"删除权限: {perm}")

        # 提交所有更改
        await db.commit()

        logger.success(
            f"权限初始化完成 - 权限 创建: {stats['permission_created']}, 删除: {stats['permission_deleted']}")
        return stats

    except Exception as e:
        logger.error(f"初始化权限失败: {e}")
        raise e

async def init_admin_role_permission(db: AsyncSessionDep):
    try:
        role_repo = RoleRepository(db)
        permission_repo = PermissionRepository(db)

        # 获取admin角色（预加载权限关系）
        admin_role = await role_repo.get_one_by_name_with_permissions("admin")

        if not admin_role:
            logger.error("admin角色不存在，请先初始化角色")
            raise ValueError("admin角色不存在，请先初始化角色")

        # 获取所有权限
        permissions = await permission_repo.get_all()

        # 初始化角色权限关系
        for permission in permissions:
            if permission not in admin_role.permissions:
                logger.debug(f"为角色 {admin_role.name} 分配权限: {permission.name}")
                admin_role.permissions.append(permission)  # type: ignore
            else:
                logger.debug(f"角色 {admin_role.name} 已经拥有权限: {permission.name}")

        await db.commit()

        return {"success":True, "message": "角色权限初始化成功"}

    except Exception as e:
        logger.error(f"角色权限初始化失败: {e}")
        raise e

# todo 创建user为admin，密码为admin的用户名，拥有所有权限。
