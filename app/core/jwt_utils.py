"""
JWT 工具模块
包含 JWT token 的编码和解码功能，避免循环导入
"""
from jose import jwt, JWTError, ExpiredSignatureError
from fastapi import HTTPException, status
from loguru import logger

from app.config.config import settings
from app.core.auth import TokenService





# def safe_log_token_info(token: str, action: str) -> str:
#     """
#     安全地记录 token 信息（只记录 JTI，不记录完整 token）
#
#     Args:
#         token: JWT token
#         action: 操作描述
#
#     Returns:
#         str: JTI 用于日志记录
#     """
#     try:
#         payload = TokenService.decode_token(token)
#         jti = payload.get("jti", "unknown")
#         logger.info(f"Token {action} - JTI: {jti}")
#         return jti
#
#     except Exception as e:
#         logger.warning(f"Token {action} - unable to extract JTI: {e}")
#         return "unknown"
