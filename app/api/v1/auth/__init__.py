"""
认证模块路由
"""
from fastapi import APIRouter
from app.api.v1.auth.login import router as login_router
from app.api.v1.auth.token import router as token_router
from app.api.v1.auth.users import router as users_router
from app.api.v1.auth.roles import router as roles_router
from app.api.v1.auth.permissions import router as permissions_router

# 创建认证模块的主路由
auth_router = APIRouter()

# 注册各个子模块的路由
auth_router.include_router(login_router, tags=["登录"])
auth_router.include_router(token_router, tags=["Token管理"])
auth_router.include_router(users_router, tags=["用户管理"])
auth_router.include_router(roles_router, tags=["角色管理"])
auth_router.include_router(permissions_router, tags=["权限管理"])
