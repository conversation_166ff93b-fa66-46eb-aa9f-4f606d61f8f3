"""
Token 相关API
包括 refresh token 刷新、查询等功能
"""
from fastapi import APIRouter, Depends, HTTPException, status, Cookie, Response, Request, Header
from typing import Optional
from loguru import logger
from datetime import datetime, timezone
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials

from app.core.auth import TokenService,AuthService
from app.core.deps import AsyncSessionDep
from app.schemas.user import (
    AccessTokenResponse, RefreshTokenInfo, RefreshTokenListResponse,
    TokenValidationResponse
)
from app.repositories.token_repository import RefreshTokenRepository, get_refresh_token_repository
from app.config.config import settings

router = APIRouter()

security = HTTPBearer()

@router.get("/refresh-access-token")
async def refresh_access_token(
        token_repo: RefreshTokenRepository = Depends(get_refresh_token_repository),
        refresh_token_from_cookie: str = <PERSON><PERSON>(None, alias="refresh_token", include_in_schema=False)
):
    """
    获取 Access Token 用于 Swagger UI 认证

    这个端点专门用于在 Swagger UI 中获取 access token，
    然后可以复制到 Authorize 按钮中使用。
    """
    if not refresh_token_from_cookie:
        return {
            "error": "No refresh token found",
            "message": "Please login first to get refresh token",
            "access_token": None,
        }

    try:
        # 验证 refresh token
        token_info = await TokenService.verify_refresh_token(refresh_token_from_cookie, token_repo)

        # 生成新的 access token
        new_access_token = TokenService.create_access_token({"sub": token_info["username"]})

        # 解码新 token 获取 jti 用于日志
        new_payload = TokenService.decode_token(new_access_token)
        jti = new_payload.get("jti")

        logger.info(f"New access token generated for Swagger user: {token_info['username']}, jti: {jti}")

        return {
            "access_token": new_access_token,
            "username": token_info["username"],
            "token_type": "bearer"
        }

    except HTTPException as e:
        return {
            "error": e.detail,
            "message": "Failed to refresh access token",
            "access_token": None,
            "suggestion": "Try logging in again to get a fresh refresh token"
        }

    except Exception as e:
        logger.error(f"Failed to refresh access token for Swagger: {e}")
        return {
            "error": str(e),
            "message": "Internal error while generating access token",
            "access_token": None
        }



@router.post("/refresh-tokens", response_model=AccessTokenResponse)
async def refresh_tokens(
        response: Response,
        token_repo: RefreshTokenRepository = Depends(get_refresh_token_repository),
        refresh_token_from_cookie: str = Cookie(None, alias="refresh_token", include_in_schema=False)
):
    """刷新访问令牌"""
    # 验证 refresh token
    token_info = await TokenService.verify_refresh_token(refresh_token_from_cookie, token_repo)

    # 刷新 tokens
    new_tokens = await TokenService.refresh_user_tokens(
        username=token_info["username"],
        old_jti=token_info["jti"],
        token_repo=token_repo
    )

    # 更新 cookie
    response.set_cookie(
        key="refresh_token",
        value=new_tokens["refresh_token"],
        max_age=settings.REFRESH_TOKEN_EXPIRE_TIME * 24 * 60 * 60,
        httponly=True,
        secure=False,  # 开发环境设为False，生产环境应设为True
        samesite="lax"
    )

    return AccessTokenResponse(
        access_token=new_tokens["access_token"],
        token_type="bearer"
    )



@router.get("/list-refresh-tokens", response_model=RefreshTokenListResponse)
async def list_refresh_tokens(
        token_repo: RefreshTokenRepository = Depends(get_refresh_token_repository)
):
    """列出所有活跃的 refresh tokens（仅用于开发环境）"""
    try:
        tokens = await token_repo.list_all_tokens()

        return RefreshTokenListResponse(
            total_tokens=len(tokens),
            tokens = tokens,
            note="出于安全考虑，生产环境不应暴露此接口"
        )

    except Exception as e:
        logger.error(f"列出 refresh tokens 失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list refresh tokens"
        )





@router.get("/get-access-token")
async def get_access_token(
    token_repo: RefreshTokenRepository = Depends(get_refresh_token_repository),
    credentials: HTTPAuthorizationCredentials = Depends(security),
    refresh_token_from_cookie: str = Cookie(None, alias="refresh_token", include_in_schema=False),

):
    """获取 access token 信息，如果无效则通过 refresh token 续签"""
    access_token = credentials.credentials if credentials else None
    scheme = credentials.scheme
    # 添加详细的调试日志
    logger.debug(f"Authorization header: {credentials}")
    logger.debug(f"Refresh token cookie length: {len(refresh_token_from_cookie) if refresh_token_from_cookie else 0}")

    # 尝试从 Authorization header 获取 access token

    if credentials:
        if scheme == "Bearer":
            logger.info(f"Access token extracted from Authorization header, length: {len(access_token)}")
        else:
            logger.warning(f"Authorization header format invalid: {access_token[:20]}...")
    else:
        logger.debug("No Authorization header found")


    # 如果有 access token，先验证是否有效
    if access_token:
        try:
            access_token_payload = TokenService.decode_token(access_token)
            exp = access_token_payload.get("exp")
            current_time = datetime.now(timezone.utc).timestamp()
            expire_seconds = exp - current_time if exp else 0

            if expire_seconds > 0:  # access token 仍然有效
                return TokenValidationResponse(
                    valid=True,
                    username=access_token_payload.get("sub"),
                    jti=access_token_payload.get("jti"),
                    token=access_token,
                    expires_seconds=int(expire_seconds),
                    message="Access token is valid"
                )
        except Exception as e:
            logger.info(f"Access token invalid: {e}, trying to refresh...")
            # 将无效的 access token 设为 None，以便继续 refresh 流程
            access_token = None
            logger.debug("Set access_token to None, continuing to refresh logic...")

    logger.debug(f"After access token validation, access_token is: {access_token}")

    if not access_token:
        logger.info("No valid access token found, trying refresh token from cookie...")
    else:
        logger.debug("Access token is still valid, skipping refresh logic")
    
    # access token 无效或不存在，尝试通过 refresh token 获取新的
    logger.debug(f"Checking refresh token, refresh_token_from_cookie length: {len(refresh_token_from_cookie) if refresh_token_from_cookie else 0}")

    if not refresh_token_from_cookie:
        logger.warning("No refresh token found in cookie")
        return TokenValidationResponse(
            valid=False,
            message="No valid access token and no refresh token found. Please login first to get refresh token.",
            error="authentication_required"
        )

    logger.info("Found refresh token, attempting to generate new access token...")


    
    try:
        # 验证 refresh token 并生成新的 access token
        token_info = await TokenService.verify_refresh_token(refresh_token_from_cookie, token_repo)
        new_access_token = TokenService.create_access_token({"sub": token_info["username"]})

        # 解码新 token 获取过期时间和 jti
        new_payload = TokenService.decode_token(new_access_token)
        exp = new_payload.get("exp")
        jti = new_payload.get("jti")
        current_time = datetime.now(timezone.utc).timestamp()
        expire_seconds = exp - current_time if exp else 0

        logger.info(f"New access token generated for user: {token_info['username']}, jti: {jti}")

        return TokenValidationResponse(
            valid=True,
            username=token_info["username"],
            jti=jti,
            token=new_access_token,
            expires_seconds=int(expire_seconds),
            message="New access token generated from refresh token"
        )
        
    except Exception as e:
        logger.error(f"Failed to get access token: {e}")
        return TokenValidationResponse(
            valid=False,
            message="Failed to get or generate access token",
            error=str(e)
        )


    # logger.info(f"Access token payload: {access_token_payload}")
    #
    #
    # return {"access_token": token, "user": current_user}


@router.get("/verify-refresh-token", response_model=TokenValidationResponse)
async def api_verify_refresh_token(
        token_repo: RefreshTokenRepository = Depends(get_refresh_token_repository),
        refresh_token: str = Cookie(None, alias="refresh_token", include_in_schema=False)
):
    """验证 refresh token 是否有效"""

    # 验证 refresh token
    token_info = await TokenService.verify_refresh_token(refresh_token, token_repo)

    logger.success(f"Token 验证成功: username={token_info['username']}, jti={token_info['jti']}")

    return token_info




@router.delete("/revoke-refresh-token")
async def revoke_refresh_token(
        response: Response,
        token_repo: RefreshTokenRepository = Depends(get_refresh_token_repository),
        refresh_token: str = Cookie(None, alias="refresh_token", include_in_schema=False)
):
    """撤销当前的 refresh token"""

    # 验证并获取 token 信息
    revoked_token_info = await TokenService.revoke_refresh_token(response, refresh_token, token_repo)
    if revoked_token_info["success"]:
        logger.success(f"Token 撤销成功: jti={revoked_token_info['jti']}")
    else:
        logger.warning(f"Token 撤销失败: jti={revoked_token_info['jti']}")

    return revoked_token_info





@router.delete("/revoke-all-refresh-tokens/{username}")
async def revoke_all_refresh_tokens(
        username: str,
        token_repo: RefreshTokenRepository = Depends(get_refresh_token_repository)
):
    """撤销指定用户的所有 refresh tokens（管理员功能）"""
    try:
        revoked_count = await token_repo.revoke_all_user_tokens(username)

        return {
            "message": f"Successfully revoked {revoked_count} refresh tokens",
            "username": username,
            "revoked_count": revoked_count,
            "note": "此接口应该有管理员权限验证"
        }

    except Exception as e:
        logger.error(f"撤销用户所有 refresh tokens 失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to revoke all refresh tokens"
        )

