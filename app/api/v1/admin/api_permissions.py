"""
API权限配置管理 - 用于管理API权限配置
"""
from fastapi import APIRouter, Depends, HTTPException, status

from app.core.deps import AsyncSessionDep
from app.core.sync_api_permission import (
    ApiPermissionSyncService
)
from app.core.auth import AuthService
from app.repositories.user_repository import ApiPermissionRepository

from app.schemas.user import SyncStatsResponse, ValidationResponse, ApiPermissionResponse

router = APIRouter(
    responses={401: {"description": "Unauthorized"}},
)


@router.post("/sync_api_permissions", response_model=SyncStatsResponse)
async def sync_api_permissions(
    db: AsyncSessionDep,

):
    """同步YAML配置到数据库"""
    try:
        stats = await ApiPermissionSyncService.sync_yml_to_db(db)
        return {"success": True, "stats": SyncStatsResponse(
            **stats,
            message="权限同步完成"
        )}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"同步失败: {str(e)}"
        )


@router.get("/validate_api_permissions_yml", response_model=ValidationResponse)
async def validate_config(

):
    """验证YAML配置文件"""
    try:
        errors = await ApiPermissionSyncService.validate_config()
        return ValidationResponse(
            is_valid= len(errors) == 0,
            errors=errors
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"验证失败: {str(e)}"
        )


@router.post("/export_db_to_yaml")
async def export_config(
    db: AsyncSessionDep,

):
    """导出数据库配置到YAML文件"""
    try:
        await ApiPermissionSyncService.export_db_to_yaml(db)
        return {"success": True,"message": "配置已导出到 app/config/api_permissions_export.yml"}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"导出失败: {str(e)}"
        )


@router.get("/get_all_api_permissions")
async def all_api_permissions(
    db: AsyncSessionDep,

):
    """获取所有API权限配置"""

    try:
        repo = ApiPermissionRepository(db)
        permissions = await repo.get_all_api_permissions()
        
        return {"success": True,"ApiPermissionResponse":
            [ApiPermissionResponse(
                id=perm.id,
                api_path=perm.api_path,
                description=perm.description,
                required_scopes=perm.required_scopes,
                is_active=perm.is_active,
                category=perm.category
            )
            for perm in permissions]
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取权限列表失败: {str(e)}"
        )


@router.put("/toggle/{api_path}")
async def toggle_api_permission(
    api_path: str,
    db: AsyncSessionDep,

):
    """切换API权限的激活状态"""
    try:
        repo = ApiPermissionRepository(db)
        result = await repo.toggle_api_permission_is_active(api_path)

        await db.commit()
        
        return {
            "message": f"权限 {api_path} 已{'激活' if result else '停用'}",
            "is_active": result
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"切换权限状态失败: {str(e)}"
        )
